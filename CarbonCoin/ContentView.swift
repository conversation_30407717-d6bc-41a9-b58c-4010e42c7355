//
//  ContentView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

struct ContentView: View {
    @EnvironmentObject private var healthManager: HealthManager
    @StateObject private var healthDataViewModel : HealthDataViewModel
    
    init(){
        // 使用未初始化的 healthManager 会报错，所以需要使用 `_healthDataViewModel`
        let healthManager = HealthManager() // 或其他方式获取
        self._healthDataViewModel = StateObject(wrappedValue: HealthDataViewModel(dataType: .steps, healthManager: healthManager))
    }

    var body: some View {
        MainTabView()
            .environmentObject(healthDataViewModel)
    }
    
}

#Preview {
    ContentView()
}
