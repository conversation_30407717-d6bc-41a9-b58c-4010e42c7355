//
//  PetModels.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/17.
//

import Foundation
import CloudKit

// MARK: - 全局图鉴模板（静态数据）
/// 宠物图鉴模板，存储宠物的基础信息
struct PetTemplate: Codable, Identifiable {
    let id = UUID()
    let name: String        // 宠物名称
    let imageName: String   // 图片资源名称
    let rarity: Int         // 稀有度（1-5星）
    let introduction: String

    // 自定义编码键，排除 id
    private enum CodingKeys: String, CodingKey {
        case name, imageName, rarity, introduction
    }
    
    // 自定义初始化方法，用于预览和其他手动创建的场景
    init(name: String, imageName: String, rarity: Int, introduction: String? = nil) {
        self.name = name
        self.imageName = imageName
        self.rarity = rarity
        self.introduction = introduction ?? "这只碳宠还没有介绍～"
    }
}

// MARK: - 用户宠物（动态数据）
/// 用户拥有的宠物实例，存储用户相关的动态数据
struct UserPet: Codable, Identifiable {
    let id: UUID
    let templateName: String    // 引用 PetTemplate.name
    var level: Int             // 宠物等级
    var experience: Int        // 当前经验值
    var lastFeedTime: Date?    // 最后喂养时间
    var createdAt: Date        // 获得时间
    var updatedAt: Date        // 最后更新时间

    init(templateName: String, level: Int = 1, experience: Int = 0) {
        self.id = UUID()
        self.templateName = templateName
        self.level = level
        self.experience = experience
        self.lastFeedTime = nil
        self.createdAt = Date()
        self.updatedAt = Date()
    }
    
    /// 经验值升级阈值
    var experienceThreshold: Int {
        return level * 100
    }

    /// 经验值进度（0.0 - 1.0）
    var experienceProgress: Double {
        return Double(experience) / Double(experienceThreshold)
    }

    /// 是否可以升级
    var canLevelUp: Bool {
        return experience >= experienceThreshold
    }

    /// 喂养成本（基于等级）
    var feedCost: Int {
        return level * 5
    }
    
    /// 升级
    mutating func levelUp() {
        if canLevelUp {
            level += 1
            experience -= experienceThreshold
            updatedAt = Date()
        }
    }

    /// 喂养
    mutating func feed(experience: Int = 10) {
        self.experience += experience
        self.lastFeedTime = Date()
        self.updatedAt = Date()
    }
}
    


// MARK: - CloudKit Support for UserPet
extension UserPet {
    /// CloudKit记录类型名称
    static let recordType = "UserPet"

    /// 从CloudKit记录创建用户宠物
    init?(from record: CKRecord) {
        guard let templateName = record["templateName"] as? String,
              let level = record["level"] as? Int,
              let experience = record["experience"] as? Int,
              let createdAt = record["createdAt"] as? Date,
              let updatedAt = record["updatedAt"] as? Date else {
            return nil
        }

        self.id = UUID(uuidString: record.recordID.recordName) ?? UUID()
        self.templateName = templateName
        self.level = level
        self.experience = experience
        self.lastFeedTime = record["lastFeedTime"] as? Date
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }

    /// 转换为CloudKit记录
    func toRecord() -> CKRecord {
        let recordID = CKRecord.ID(recordName: id.uuidString)
        let record = CKRecord(recordType: UserPet.recordType, recordID: recordID)

        record["templateName"] = templateName
        record["level"] = level
        record["experience"] = experience
        record["lastFeedTime"] = lastFeedTime
        record["createdAt"] = createdAt
        record["updatedAt"] = updatedAt

        return record
    }
}

// MARK: - 组合视图模型
/// 用于视图显示的组合数据结构
struct PetDisplayModel: Identifiable {
    let id: UUID
    let template: PetTemplate
    let userPet: UserPet?

    /// 是否已获得
    var isOwned: Bool {
        return userPet != nil
    }

    /// 显示等级（未获得时显示1）
    var displayLevel: Int {
        return userPet?.level ?? 1
    }

    /// 显示经验进度（未获得时显示0）
    var displayExperienceProgress: Double {
        return userPet?.experienceProgress ?? 0.0
    }

    /// 喂养成本
    var feedCost: Int {
        return userPet?.feedCost ?? 5
    }

    init(template: PetTemplate, userPet: UserPet? = nil) {
        self.id = template.id
        self.template = template
        self.userPet = userPet
    }
}
