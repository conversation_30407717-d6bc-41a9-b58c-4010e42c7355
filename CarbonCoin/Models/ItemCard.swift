//
//  ItemCard.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/23.
//

import Foundation
import SwiftUI

// 卡片模型
struct ItemCard: Codable, Identifiable, Equatable {
    let id: UUID
    let tags: [String]
    let description: String
    let title: String
    let imagePath: String // 图像存储路径（Documents 目录）
    let createdAt: Date // 创建时间戳

    // 从 Data 加载图像
    var image: UIImage? {
        if let data = try? Data(contentsOf: URL(fileURLWithPath: imagePath)) {
            return UIImage(data: data)
        }
        return nil
    }

    // 格式化创建时间显示
    var formattedCreatedAt: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: createdAt)
    }
}

// 卡片存储管理
class CardStore: ObservableObject {
    @Published var cards: [ItemCard] = []
    private let userDefaultsKey = "SavedCards"
    
    init() {
        loadCards()
    }
    
    // 保存卡片到 UserDefaults 和图像到文件系统
    func saveCard(tags: [String], description: String, title: String, imageData: Data) {
        let id = UUID()
        let imagePath = saveImageToDocuments(imageData: imageData, id: id)
        let card = ItemCard(id: id, tags: tags, description: description, title: title, imagePath: imagePath, createdAt: Date())
        cards.append(card)
        saveCards()
    }
    
    // 从 UserDefaults 加载卡片
    private func loadCards() {
        if let data = UserDefaults.standard.data(forKey: userDefaultsKey),
           let savedCards = try? JSONDecoder().decode([ItemCard].self, from: data) {
            cards = savedCards
        }
    }
    
    // 保存卡片到 UserDefaults
    private func saveCards() {
        if let data = try? JSONEncoder().encode(cards) {
            UserDefaults.standard.set(data, forKey: userDefaultsKey)
        }
    }
    
    // 保存图像到 Documents 目录
    private func saveImageToDocuments(imageData: Data, id: UUID) -> String {
        let fileName = "\(id.uuidString).jpg"
        let url = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!.appendingPathComponent(fileName)
        try? imageData.write(to: url)
        return url.path
    }
}
