//
//  User.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import Foundation
import CoreLocation
import CloudKit

/// 用户模型
struct User: Codable, Identifiable {
    let id: UUID
    var username: String
    var carbonCoins: Int
    var location: CLLocation?
    var shareLocation: Bool
    var createdAt: Date
    var updatedAt: Date
    
    init(id: UUID = UUID(), 
         username: String, 
         carbonCoins: Int = 0, 
         location: CLLocation? = nil, 
         shareLocation: Bool = false) {
        self.id = id
        self.username = username
        self.carbonCoins = carbonCoins
        self.location = location
        self.shareLocation = shareLocation
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

// MARK: - CloudKit Support
extension User {
    /// CloudKit记录类型名称
    static let recordType = "User"
    
    /// 从CloudKit记录创建用户
    init?(from record: CKRecord) {
        guard let username = record["username"] as? String,
              let carbonCoins = record["carbonCoins"] as? Int,
              let shareLocation = record["shareLocation"] as? Bool,
              let createdAt = record["createdAt"] as? Date,
              let updatedAt = record["updatedAt"] as? Date else {
            return nil
        }
        
        self.id = UUID(uuidString: record.recordID.recordName) ?? UUID()
        self.username = username
        self.carbonCoins = carbonCoins
        self.shareLocation = shareLocation
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        
        // 处理位置信息
        if let locationData = record["location"] as? Data {
            self.location = try? NSKeyedUnarchiver.unarchivedObject(ofClass: CLLocation.self, from: locationData)
        } else {
            self.location = nil
        }
    }
    
    /// 转换为CloudKit记录
    func toRecord() -> CKRecord {
        let recordID = CKRecord.ID(recordName: id.uuidString)
        let record = CKRecord(recordType: User.recordType, recordID: recordID)
        
        record["username"] = username
        record["carbonCoins"] = carbonCoins
        record["shareLocation"] = shareLocation
        record["createdAt"] = createdAt
        record["updatedAt"] = updatedAt
        
        // 处理位置信息
        if let location = location {
            if let locationData = try? NSKeyedArchiver.archivedData(withRootObject: location, requiringSecureCoding: true) {
                record["location"] = locationData
            }
        }
        
        return record
    }
}

// MARK: - Codable Support for CLLocation
extension User {
    enum CodingKeys: String, CodingKey {
        case id, username, carbonCoins, shareLocation, createdAt, updatedAt
        case latitude, longitude, altitude, timestamp
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        id = try container.decode(UUID.self, forKey: .id)
        username = try container.decode(String.self, forKey: .username)
        carbonCoins = try container.decode(Int.self, forKey: .carbonCoins)
        shareLocation = try container.decode(Bool.self, forKey: .shareLocation)
        createdAt = try container.decode(Date.self, forKey: .createdAt)
        updatedAt = try container.decode(Date.self, forKey: .updatedAt)
        
        // 处理位置信息
        if let latitude = try container.decodeIfPresent(Double.self, forKey: .latitude),
           let longitude = try container.decodeIfPresent(Double.self, forKey: .longitude) {
            let altitude = try container.decodeIfPresent(Double.self, forKey: .altitude) ?? 0
            let timestamp = try container.decodeIfPresent(Date.self, forKey: .timestamp) ?? Date()
            location = CLLocation(
                coordinate: CLLocationCoordinate2D(latitude: latitude, longitude: longitude),
                altitude: altitude,
                horizontalAccuracy: 5.0,
                verticalAccuracy: 5.0,
                timestamp: timestamp
            )
        } else {
            location = nil
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        try container.encode(id, forKey: .id)
        try container.encode(username, forKey: .username)
        try container.encode(carbonCoins, forKey: .carbonCoins)
        try container.encode(shareLocation, forKey: .shareLocation)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encode(updatedAt, forKey: .updatedAt)
        
        // 处理位置信息
        if let location = location {
            try container.encode(location.coordinate.latitude, forKey: .latitude)
            try container.encode(location.coordinate.longitude, forKey: .longitude)
            try container.encode(location.altitude, forKey: .altitude)
            try container.encode(location.timestamp, forKey: .timestamp)
        }
    }
}
