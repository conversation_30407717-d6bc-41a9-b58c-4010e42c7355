//
//  ImageAPI.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/23.
//

import Foundation
import SwiftUI

// 输入模型
struct ImageUploadData: Encodable {
    let filename: String
    // 图像本身作为 Data，不在 JSON 中编码，而在 multipart body 中
}

// 输出模型（多标签）
struct ImageResponse: Decodable {
    let labels: [String]
    let description: String
}

@MainActor
class APIService: ObservableObject {
    @Published var labels: [String] = []
    @Published var description: String = ""
    @Published var isLoading = false
    @Published var errorMessage: String? = nil
    
    func uploadImage(_ imageData: Data, filename: String = "image.jpg") async {
        isLoading = true
        errorMessage = nil
        
        guard let url = URL(string: "https://your-api-endpoint.com/upload") else {
            errorMessage = "Invalid URL"
            isLoading = false
            return
        }
        
        // 创建 multipart/form-data 请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        let boundary = "Boundary-\(UUID().uuidString)"
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
        request.setValue("your_api_key", forHTTPHeaderField: "x-api-key")
        
        // 构建 multipart body
        request.httpBody = createMultipartBody(imageData: imageData, filename: filename, boundary: boundary)
        
        do {
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse, (200...299).contains(httpResponse.statusCode) else {
                errorMessage = "Server error or invalid response"
                isLoading = false
                return
            }
            
            // 解码响应
            let decodedResponse = try JSONDecoder().decode(ImageResponse.self, from: data)
            labels = decodedResponse.labels
            description = decodedResponse.description
            
            // 处理逻辑示例
            if !decodedResponse.labels.isEmpty {
                print("成功：标签 = \(decodedResponse.labels), 描述 = \(decodedResponse.description)")
            } else {
                print("失败：无标签")
                errorMessage = "No labels returned"
            }
            
            isLoading = false
        } catch {
            errorMessage = "Failed to upload image: \(error.localizedDescription)"
            isLoading = false
        }
    }
    
    private func createMultipartBody(imageData: Data, filename: String, boundary: String) -> Data {
        var body = Data()
        
        // 添加图像字段
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"image\"; filename=\"\(filename)\"\r\n".data(using: .utf8)!)
        body.append("Content-Type: image/jpeg\r\n\r\n".data(using: .utf8)!)
        body.append(imageData)
        body.append("\r\n".data(using: .utf8)!)
        
        // 结束 boundary
        body.append("--\(boundary)--\r\n".data(using: .utf8)!)
        
        return body
    }
}
