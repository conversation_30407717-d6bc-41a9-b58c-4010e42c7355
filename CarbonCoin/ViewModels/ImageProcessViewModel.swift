//
//  ImageProcessViewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/22.
//

import SwiftUI
import PhotosUI
import Vision
import CoreImage
import Photos

class ImageProcessViewModel: ObservableObject {
    @Published var selectedPhoto: PhotosPickerItem?
    @Published var inputImage: UIImage?
    @Published var processedImage: UIImage?
    @Published var subjectObservation: VNInstanceMaskObservation?
    @Published var subjects: [ImageProcess.SubjectInfo] = [] // 所有检测到的主体
    @Published var selectedSubjectIndices: Set<Int> = [] // 选中的主体索引
    @Published var showPhotoPicker = false
    @Published var showSaveAlert = false
    @Published var isProcessing = false
    @Published var showPermissionAlert = false
    @Published var showSubjectSelection = false // 显示主体选择状态

    private let imageProcessingService = ImageProcess()

    @MainActor
    func checkPhotoLibraryPermission() async -> Bool {
        let status = PHPhotoLibrary.authorizationStatus(for: .readWrite)
        switch status {
        case .authorized, .limited:
            return true
        case .notDetermined:
            return await withCheckedContinuation { continuation in
                PHPhotoLibrary.requestAuthorization(for: .readWrite) { newStatus in
                    continuation.resume(returning: newStatus == .authorized || newStatus == .limited)
                }
            }
        case .denied, .restricted:
            showPermissionAlert = true
            return false
        @unknown default:
            return false
        }
    }

    @MainActor
    func loadImage() {
        Task {
            // 检查相册权限
            guard await checkPhotoLibraryPermission() else {
                print("相册权限未授予")
                return
            }

            guard let selectedPhoto = selectedPhoto,
                  let data = try? await selectedPhoto.loadTransferable(type: Data.self),
                  let uiImage = UIImage(data: data) else {
                print("无法加载图片数据")
                return
            }

            inputImage = uiImage
            // 检测主体
            if let observation = imageProcessingService.detectSubjects(in: uiImage) {
                subjectObservation = observation
                // 计算所有主体的中心位置
                subjects = imageProcessingService.calculateSubjectCenters(from: observation)
                showSubjectSelection = !subjects.isEmpty
            } else {
                subjectObservation = nil
                subjects = []
                showSubjectSelection = false
            }

            processedImage = nil
            selectedSubjectIndices.removeAll()
        }
    }

    // MARK: - 处理主体选择
    @MainActor
    func handleImageTap(at position: CGPoint) {
        // 查找最近的主体
        if let nearestSubject = imageProcessingService.findNearestSubject(at: position, in: subjects) {
            toggleSubjectSelection(nearestSubject.index)
        }
    }

    @MainActor
    func toggleSubjectSelection(_ index: Int) {
        if selectedSubjectIndices.contains(index) {
            selectedSubjectIndices.remove(index)
        } else {
            selectedSubjectIndices.insert(index)
        }
    }

    @MainActor
    func selectAllSubjects() {
        selectedSubjectIndices = Set(subjects.map { $0.index })
    }

    @MainActor
    func clearSelection() {
        selectedSubjectIndices.removeAll()
    }

    @MainActor
    func processImage() {
        guard let inputImage = inputImage else { return }
        isProcessing = true

        Task {
            if selectedSubjectIndices.isEmpty {
                // 如果没有选择主体，提取所有主体
                processedImage = imageProcessingService.extractSubject(
                    from: inputImage,
                    croppedToInstancesExtent: true
                )
            } else {
                // 提取选中的主体
                processedImage = imageProcessingService.extractMultipleSubjects(
                    from: inputImage,
                    selectedIndices: selectedSubjectIndices,
                    croppedToInstancesExtent: true
                )
            }
            isProcessing = false
        }
    }

    @MainActor
    func saveImage() {
        guard let processedImage = processedImage else { return }
        imageProcessingService.saveImageToAlbum(processedImage) { success, error in
            if success {
                self.showSaveAlert = true
            }
        }
    }
}
