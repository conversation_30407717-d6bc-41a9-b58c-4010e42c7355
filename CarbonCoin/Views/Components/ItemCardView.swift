//
//  ItemCardView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/23.
//

import SwiftUI

struct ItemCardView: View {
    let card: ItemCard
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 图像
            if let image = card.image {
                Image(uiImage: image)
                    .resizable()
                    .scaledToFit()
                    .frame(maxWidth: .infinity, maxHeight: 180)
                    .clipShape(RoundedRectangle(cornerRadius: 10))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                    )
            } else {
                Rectangle()
                    .fill(.gray.opacity(0.2))
                    .frame(maxWidth: .infinity, maxHeight: 180)
                    .overlay(
                        Text("Image Unavailable")
                            .foregroundColor(.white)
                            .font(.caption)
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 10))
            }
            
            // 标题
            Text(card.title)
                .font(.title3)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
                .padding(.top, 4)
            
            // 标签
            HStack {
                ForEach(card.tags, id: \.self) { tag in
                    Text(tag)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.blue.opacity(0.15))
                        .clipShape(Capsule())
                        .foregroundColor(.blue)
                }
            }
            
            // 描述
            Text(card.description)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .lineLimit(2)
                .padding(.top, 4)
        }
        .padding()
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: .gray.opacity(0.2), radius: 4, x: 0, y: 2)
        .padding(.horizontal, 8)
        .onTapGesture {
            print("Tapped card: \(card.title) (ID: \(card.id))")
        }
    }
}

struct ItemCardView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            ItemCardView(card: ItemCard(
                id: UUID(),
                tags: ["粉色头发", "绿色眼睛", "黑色发饰"],
                description: "这是一张动漫角色阿尼亚·福杰的特写插画。",
                title: "阿尼亚插画",
                imagePath: "",
                createdAt: Date()
            ))
            .previewLayout(.sizeThatFits)
            .padding()
            .background(Color.gray.opacity(0.1))
            .previewDisplayName("Light Mode")
            
            ItemCardView(card: ItemCard(
                id: UUID(),
                tags: ["塑料瓶身", "纸质杯托"],
                description: "这是一个赛百味品牌的杯子。",
                title: "赛百味杯子",
                imagePath: "",
                createdAt: Date()
            ))
            .previewLayout(.sizeThatFits)
            .padding()
            .background(Color.gray.opacity(0.1))
            .environment(\.colorScheme, .dark)
            .previewDisplayName("Dark Mode")
        }
    }
}
