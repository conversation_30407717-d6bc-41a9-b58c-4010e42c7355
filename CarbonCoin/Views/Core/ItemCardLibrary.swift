//
//  ItemCardLibrary.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/24.
//

import SwiftUI

struct ItemCardLibrary: View {
    @EnvironmentObject var cardStore: CardStore
    @State private var searchText = ""
    @State private var selectedCard: ItemCard?
    @State private var showCardDetail = false

    // 过滤后的卡片
    private var filteredCards: [ItemCard] {
        if searchText.isEmpty {
            return cardStore.cards.sorted { $0.createdAt > $1.createdAt }
        } else {
            return cardStore.cards
                .filter { $0.title.localizedCaseInsensitiveContains(searchText) }
                .sorted { $0.createdAt > $1.createdAt }
        }
    }

    var body: some View {
        NavigationStack {
            ZStack {
                CustomAngularGradient()

                VStack(spacing: 0) {
                    // 搜索栏
                    searchBar
                        .padding(.horizontal, Theme.Spacing.md)
                        .padding(.top, Theme.Spacing.sm)

                    if filteredCards.isEmpty {
                        emptyStateView
                    } else {
                        // 卡片网格
                        ScrollView {
                            LazyVGrid(columns: [
                                GridItem(.flexible(), spacing: Theme.Spacing.sm),
                                GridItem(.flexible(), spacing: Theme.Spacing.sm)
                            ], spacing: Theme.Spacing.md) {
                                ForEach(filteredCards) { card in
                                    ItemCardThumbnailView(card: card) {
                                        selectedCard = card
                                        showCardDetail = true
                                    }
                                }
                            }
                            .padding(.horizontal, Theme.Spacing.md)
                            .padding(.bottom, Theme.Spacing.tab)
                        }
                        .scrollContentBackground(.hidden)
                    }
                }
            }
            .navigationTitle("卡片库")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(.clear, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .sheet(isPresented: $showCardDetail) {
                if let card = selectedCard {
                    ItemCardDetailView(card: card)
                }
            }
        }
    }

    // MARK: - 搜索栏
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.textSecondary)

            TextField("搜索卡片标题...", text: $searchText)
                .font(.bodyBrand)
                .foregroundColor(.textPrimary)
        }
        .padding(.horizontal, Theme.Spacing.md)
        .padding(.vertical, Theme.Spacing.sm)
        .background(Color.cardBackground.opacity(0.3))
        .cornerRadius(Theme.CornerRadius.md)
        .glassCard()
    }

    // MARK: - 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: Theme.Spacing.lg) {
            Spacer()

            Image(systemName: "folder")
                .font(.system(size: 60))
                .foregroundColor(.textSecondary)

            VStack(spacing: Theme.Spacing.sm) {
                Text(searchText.isEmpty ? "暂无卡片" : "未找到匹配的卡片")
                    .font(.title3Brand)
                    .foregroundColor(.textPrimary)

                Text(searchText.isEmpty ? "使用图像处理功能创建您的第一张卡片" : "尝试使用其他关键词搜索")
                    .font(.bodyBrand)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
            }

            Spacer()
        }
        .padding(.horizontal, Theme.Spacing.lg)
    }
}

// MARK: - 卡片缩略图视图
struct ItemCardThumbnailView: View {
    let card: ItemCard
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                // 图像
                if let image = card.image {
                    Image(uiImage: image)
                        .resizable()
                        .scaledToFill()
                        .frame(height: 120)
                        .clipped()
                        .cornerRadius(Theme.CornerRadius.md)
                } else {
                    Rectangle()
                        .fill(Color.cardBackground.opacity(0.3))
                        .frame(height: 120)
                        .cornerRadius(Theme.CornerRadius.md)
                        .overlay(
                            Image(systemName: "photo")
                                .font(.title2)
                                .foregroundColor(.textSecondary)
                        )
                }

                // 标题
                Text(card.title)
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)

                // 标签（最多显示2个）
                HStack {
                    ForEach(Array(card.tags.prefix(2)), id: \.self) { tag in
                        Text(tag)
                            .font(.caption2)
                            .foregroundColor(.textPrimary)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.brandGreen.opacity(0.2))
                            .cornerRadius(Theme.CornerRadius.xs)
                    }

                    if card.tags.count > 2 {
                        Text("+\(card.tags.count - 2)")
                            .font(.caption2)
                            .foregroundColor(.textSecondary)
                    }

                    Spacer()
                }

                // 创建时间
                Text(card.formattedCreatedAt)
                    .font(.caption2)
                    .foregroundColor(.textSecondary)
            }
            .padding(Theme.Spacing.sm)
            .background(Color.cardBackground.opacity(0.1))
            .cornerRadius(Theme.CornerRadius.lg)
            .glassCard()
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 卡片详情视图
struct ItemCardDetailView: View {
    let card: ItemCard
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationStack {
            ZStack {
                CustomAngularGradient()

                ScrollView {
                    VStack(alignment: .leading, spacing: Theme.Spacing.lg) {
                        // 图像
                        if let image = card.image {
                            Image(uiImage: image)
                                .resizable()
                                .scaledToFit()
                                .frame(maxHeight: 300)
                                .cornerRadius(Theme.CornerRadius.lg)
                                .glassCard()
                        }

                        // 标题和时间
                        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                            Text(card.title)
                                .font(.title2Brand)
                                .foregroundColor(.textPrimary)

                            Text("创建于 \(card.formattedCreatedAt)")
                                .font(.captionBrand)
                                .foregroundColor(.textSecondary)
                        }
                        .padding(Theme.Spacing.md)
                        .glassCard()

                        // 描述
                        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                            Text("描述")
                                .font(.title3Brand)
                                .foregroundColor(.textPrimary)

                            Text(card.description)
                                .font(.bodyBrand)
                                .foregroundColor(.textSecondary)
                        }
                        .padding(Theme.Spacing.md)
                        .glassCard()

                        // 标签
                        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                            Text("标签")
                                .font(.title3Brand)
                                .foregroundColor(.textPrimary)

                            LazyVGrid(columns: [
                                GridItem(.adaptive(minimum: 80), spacing: 8)
                            ], spacing: 8) {
                                ForEach(card.tags, id: \.self) { tag in
                                    Text(tag)
                                        .font(.captionBrand)
                                        .foregroundColor(.textPrimary)
                                        .padding(.horizontal, Theme.Spacing.sm)
                                        .padding(.vertical, 4)
                                        .background(Color.brandGreen.opacity(0.2))
                                        .cornerRadius(Theme.CornerRadius.sm)
                                }
                            }
                        }
                        .padding(Theme.Spacing.md)
                        .glassCard()

                        Spacer(minLength: Theme.Spacing.tab)
                    }
                    .padding(.horizontal, Theme.Spacing.md)
                }
                .scrollContentBackground(.hidden)
            }
            .navigationTitle("卡片详情")
            .navigationBarTitleDisplayMode(.inline)
            .toolbarBackground(.clear, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                    .foregroundColor(.brandGreen)
                }
            }
        }
    }
}

#Preview {
    let cardStore = CardStore()
    ItemCardLibrary()
        .environmentObject(cardStore)
}
