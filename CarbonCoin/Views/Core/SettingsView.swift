//
//  SettingsView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/20.
//

import SwiftUI
import UIKit

struct SettingsView: View {
    @EnvironmentObject var appSettings: AppSettings
    
    @State private var showImagePicker = false
    @State private var selectedImage: UIImage?
    @State private var showAvatarActionSheet = false
    @State private var shareLocation = true
    @State private var pushNotifications = true
    
    // MARK: 设置页主体
    var body: some View {
        NavigationStack{
            ZStack{
                CustomAngularGradient()
                
                ScrollView{
                    VStack(spacing: Theme.Spacing.lg){
                        // 用户信息卡片
                        AvatarCard()
                            .frame(width: UIScreen.main.bounds.width * 0.8)
                        
                        UserInfoSection()
                        
                        // 隐私设置
                        PrivacySettingsSection(
                            shareLocation: $shareLocation,
                            pushNotifications: $pushNotifications
                        )
                        
                        // 其他设置
                        OtherSettingsSection()
                        
                        Spacer(minLength: Theme.Spacing.tab) // 为底部TabBar留出空间
                    }
                    .padding(.horizontal, Theme.Spacing.md)
                    .padding(.top, Theme.Spacing.md)
                }
            }
            .navigationTitle("设置")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(.clear, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
        }
    }

}

// MARK: 用户头像与名称信息
struct AvatarCard: View{
    @EnvironmentObject var appSettings: AppSettings
    private var avatarSize: CGFloat = 80
    
    @State private var showImagePicker = false
    @State private var selectedImage: UIImage?
    @State private var showAvatarActionSheet = false
    
    var body: some View{
        ZStack{
            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                .fill(Color.cardBackground)
            
            VStack(spacing: 4){
                Button(action:{
                    showAvatarActionSheet = true
                })
                {
                    appSettings.settings.getAvatarSwiftUIImage()
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: avatarSize, height: avatarSize)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                }
                
                 
                Text(appSettings.settings.nickname)
                        .font(.title3Brand)
                        .padding(.top, 8)
                
                
                Text(appSettings.settings.userId)
                    .font(.captionBrand)
    
            }
        }
        .frame( height: 180)
        .glassCard()
        .actionSheet(isPresented: $showAvatarActionSheet) {
            ActionSheet(
                title: Text("选择头像"),
                buttons: [
                    .default(Text("使用系统默认")) {
                        
                    },
                    .default(Text("从相册选择")) {
                        showImagePicker = true
                    },
                    .cancel()
                ]
            )
        }
        .sheet(isPresented: $showImagePicker) {
            ImagePicker(selectedImage: $selectedImage)
        }
        .onChange(of: selectedImage) { _, newImage in
            if let image = newImage {
                appSettings.updateSettings { settings in
                   settings.setCustomAvatar(from: image)
               }
            }
        }
    }
}

// 图片选择器
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Environment(\.presentationMode) var presentationMode
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker
        
        init(_ parent: ImagePicker) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImage = image
            }
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}


// MARK: 个人信息编辑
struct UserInfoSection: View{
    @EnvironmentObject var appSettings: AppSettings
    
    @State private var isEditingNickname = false
    
    
    var body: some View{
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
                    // 昵称行
                    HStack {
                        Image(systemName: "person.text.rectangle")
                            .foregroundColor(.blue.opacity(0.8))
                        
                        Text("昵称")
                            .foregroundColor(.primary)

                        Spacer()

                        TextField("昵称", text: $appSettings.nickname)
                            .multilineTextAlignment(.trailing)
                            .foregroundColor(.secondary)
                    }
                    
                    // 用户ID
                    HStack {
                        Image(systemName: "person.text.rectangle")
                            .foregroundColor(.cyan.opacity(0.8))
                        
                        Text("用户ID")
                            .foregroundColor(.primary)

                        Spacer()

                        TextField("用户ID", text: $appSettings.userId)
                            .multilineTextAlignment(.trailing)
                            .foregroundColor(.secondary)
                    }
                    
                }
                .padding(Theme.Spacing.lg)
                .glassCard()
    }
}


// MARK: 隐私设置
struct PrivacySettingsSection: View {
    @Binding var shareLocation: Bool
    @Binding var pushNotifications: Bool

    var body: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            Text("隐私设置")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            VStack(spacing: Theme.Spacing.md) {
                SettingToggleRow(
                    icon: "location.fill",
                    title: "位置共享",
                    description: "允许好友查看我的位置",
                    isOn: $shareLocation
                )

                SettingToggleRow(
                    icon: "bell.fill",
                    title: "推送通知",
                    description: "接收好友消息和系统通知",
                    isOn: $pushNotifications
                )
            }
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }
}


// MARK: 其他信息与设置
struct OtherSettingsSection: View {
    @EnvironmentObject var appSettings: AppSettings
    
    var body: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            Text("其他")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            VStack(spacing: Theme.Spacing.sm) {
                SettingRow(icon: "info.circle", title: "关于应用", subtitle: "版本 1.0.0")
                SettingRow(icon: "envelope", title: "意见反馈", subtitle: "")
                SettingRow(icon: "questionmark.circle", title: "帮助中心", subtitle: "")
                SettingRow(icon: "arrow.right.square", title: "退出登录", subtitle: "", isDestructive: true)
                Text("CarbonCoin已经陪伴您 \(appSettings.settings.appUsageDays) 天了")
                    .font(.captionBrand)
                    .foregroundColor(.secondary)
            }
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }
}


// MARK: - Setting Toggle Row
struct SettingToggleRow: View {
    let icon: String
    let title: String
    let description: String
    @Binding var isOn: Bool

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.auxiliaryYellow)
                .frame(width: 24)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)

                Text(description)
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
            }

            Spacer()

            Toggle("", isOn: $isOn)
                .toggleStyle(SwitchToggleStyle(tint: Color.brandGreen))
        }
    }
}

// MARK: - Setting Row
struct SettingRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let isDestructive: Bool

    init(icon: String, title: String, subtitle: String, isDestructive: Bool = false) {
        self.icon = icon
        self.title = title
        self.subtitle = subtitle
        self.isDestructive = isDestructive
    }

    var body: some View {
        Button(action: {}) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(isDestructive ? .error : .auxiliaryYellow)
                    .frame(width: 24)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.bodyBrand)
                        .foregroundColor(isDestructive ? .error : .textPrimary)

                    if !subtitle.isEmpty {
                        Text(subtitle)
                            .font(.captionBrand)
                            .foregroundColor(.textSecondary)
                    }
                }

                Spacer()

                if !isDestructive {
                    Image(systemName: "chevron.right")
                        .foregroundColor(.textSecondary)
                        .font(.caption)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .padding(.vertical, Theme.Spacing.xs)
    }
}



#Preview {
    let appSettings = AppSettings()
    SettingsView()
        .environmentObject(appSettings)
}
