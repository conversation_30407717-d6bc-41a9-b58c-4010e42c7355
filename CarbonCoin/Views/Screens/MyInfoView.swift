//
//  MyInfoView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

struct MyInfoView: View {
    @State private var shareLocation = true
    @State private var pushNotifications = true
    @State private var searchText = ""

    var body: some View {
        NavigationStack {
            ZStack{
                CustomAngularGradient()
                
                ScrollView {
                    VStack(spacing: Theme.Spacing.lg) {
                        // 用户信息卡片
                        NavigationLink{
                            SettingsView()
                        } label:{
                            UserProfileCard()
                        }
                        
                        // 好友管理
                        FriendManagementSection(searchText: $searchText)
                    
                        
                        Spacer(minLength: 100) // 为底部TabBar留出空间
                    }
                    .padding(.horizontal, Theme.Spacing.md)
                    .padding(.top, Theme.Spacing.md)
                }
            }
            .navigationTitle("我的")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(.clear, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
        }
    }
}

// MARK: - User Profile Card
struct UserProfileCard: View {
    @EnvironmentObject var appSettings: AppSettings
    
    var body: some View {
        HStack(spacing: Theme.Spacing.md) {
            // 头像
            Circle()
                .fill(Color.primaryGradient)
                .frame(width: 60, height: 60)
                .overlay(
                    appSettings.settings.getAvatarSwiftUIImage()
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                )

            VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
                Text(appSettings.nickname)
                    .font(.title3Brand)
                    .foregroundColor(.textPrimary)

                Text(appSettings.userId)
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)

                HStack {
                    Image(systemName: "leaf.fill")
                        .foregroundColor(.auxiliaryYellow)
                        .font(.caption)

                    Text("碳币: 128")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                }
            }

            Spacer()

            Button(action: {}) {
                Image(systemName: "pencil")
                    .foregroundColor(.textSecondary)
            }
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }
}

// MARK: - Friend Management Section
struct FriendManagementSection: View {
    @Binding var searchText: String

    var body: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            Text("好友管理")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            // 搜索栏
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.textSecondary)

                TextField("搜索用户名", text: $searchText)
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)
                    .textFieldStyle(PlainTextFieldStyle())
            }
            .padding(Theme.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                    .fill(Color.glassBackground)
            )

            // 好友列表占位
            VStack(spacing: Theme.Spacing.sm) {
                FriendRow(name: "小明", status: "在线", isOnline: true)
                FriendRow(name: "小红", status: "2小时前", isOnline: false)
                FriendRow(name: "小李", status: "在线", isOnline: true)
            }
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }
}

// MARK: - Friend Row
struct FriendRow: View {
    let name: String
    let status: String
    let isOnline: Bool

    var body: some View {
        HStack {
            // 头像
            Circle()
                .fill(Color.primaryGradient.opacity(0.7))
                .frame(width: 40, height: 40)
                .overlay(
                    Text(String(name.prefix(1)))
                        .font(.bodyBrand)
                        .foregroundColor(.white)
                )

            VStack(alignment: .leading, spacing: 2) {
                Text(name)
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)

                Text(status)
                    .font(.captionBrand)
                    .foregroundColor(isOnline ? .success : .textSecondary)
            }

            Spacer()

            // 在线状态指示器
            Circle()
                .fill(isOnline ? Color.success : Color.textSecondary)
                .frame(width: 8, height: 8)
        }
        .padding(.vertical, Theme.Spacing.xs)
    }
}



#Preview {
    MyInfoView()
        .stableBackground()
}
