//
//  ScanView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

struct ScanView: View {
    @State private var isScanning = false

    var body: some View {
        NavigationStack {
            ZStack{
                CustomAngularGradient()
                
                ScrollView{
                    VStack(spacing: Theme.Spacing.xl) {
                        // 顶部留白卡片
                        NavigationLink{
                            ImageProcessView()
                        }label:{
                            ARPlaceholderCard()
                        }                      
                        
                        // 扫描区域
                        ScanAreaView(isScanning: $isScanning)
                        
                        // 扫描按钮
                        ScanButton(isScanning: $isScanning)
                        
                        // 最近扫描结果
                        RecentScanResults()
                        
                        Spacer(minLength: 100) // 为底部TabBar留出空间
                    }
                    .padding(.horizontal, Theme.Spacing.md)
                }
                .scrollContentBackground(.hidden)
            }
            .navigationTitle("扫码")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(.clear, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    NavigationLink(destination: ItemCardLibrary()) {
                        Image(systemName: "folder.fill")
                            .foregroundColor(.brandGreen)
                            .font(.title3)
                    }
                }
            }
        }
    }
}

// MARK: - AR Placeholder Card
struct ARPlaceholderCard: View {
    var body: some View {
        VStack(spacing: Theme.Spacing.md) {
            Image(systemName: "arkit")
                .font(.system(size: 40))
                .foregroundColor(.skyBlue)

            Text("扫描商品碳影响")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            Text("未来将融入AR功能，敬请期待")
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding(Theme.Spacing.xl)
        .glassCard()
    }
}

// MARK: - Scan Area View
struct ScanAreaView: View {
    @Binding var isScanning: Bool
    @State private var scanLineOffset: CGFloat = -100

    var body: some View {
        ZStack {
            // 相机预览占位
            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                .fill(Color.black.opacity(0.8))
                .frame(height: 250)
                .overlay(
                    VStack {
                        Image(systemName: "camera.fill")
                            .font(.system(size: 50))
                            .foregroundColor(.textSecondary)

                        Text("相机预览")
                            .font(.bodyBrand)
                            .foregroundColor(.textSecondary)
                    }
                )

            // 扫描框
            RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                .stroke(Color.primaryGradient, lineWidth: 3)
                .frame(width: 200, height: 200)

            // 扫描线动画
            if isScanning {
                Rectangle()
                    .fill(Color.auxiliaryYellow)
                    .frame(width: 180, height: 2)
                    .offset(y: scanLineOffset)
                    .onAppear {
                        withAnimation(
                            Animation.linear(duration: 2.0)
                                .repeatForever(autoreverses: true)
                        ) {
                            scanLineOffset = 100
                        }
                    }
                    .onDisappear {
                        scanLineOffset = -100
                    }
            }

            // 扫描框角标
            VStack {
                HStack {
                    ScanCorner(rotation: 0)
                    Spacer()
                    ScanCorner(rotation: 90)
                }
                Spacer()
                HStack {
                    ScanCorner(rotation: 270)
                    Spacer()
                    ScanCorner(rotation: 180)
                }
            }
            .frame(width: 200, height: 200)
        }
    }
}

// MARK: - Scan Corner
struct ScanCorner: View {
    let rotation: Double

    var body: some View {
        Path { path in
            path.move(to: CGPoint(x: 0, y: 20))
            path.addLine(to: CGPoint(x: 0, y: 0))
            path.addLine(to: CGPoint(x: 20, y: 0))
        }
        .stroke(Color.auxiliaryYellow, lineWidth: 3)
        .frame(width: 20, height: 20)
        .rotationEffect(.degrees(rotation))
    }
}

// MARK: - Scan Button
struct ScanButton: View {
    @Binding var isScanning: Bool

    var body: some View {
        Button(action: toggleScanning) {
            HStack {
                Image(systemName: isScanning ? "stop.circle.fill" : "qrcode.viewfinder")
                    .foregroundColor(.textPrimary)

                Text(isScanning ? "停止扫描" : "开始扫描")
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)
            }
            .frame(maxWidth: .infinity)
        }
        .buttonStyle(PrimaryButtonStyle())
    }

    private func toggleScanning() {
        withAnimation(Theme.AnimationStyle.bouncy) {
            isScanning.toggle()
        }
    }
}

// MARK: - Recent Scan Results
struct RecentScanResults: View {
    var body: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            Text("最近扫描")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            VStack(spacing: Theme.Spacing.sm) {
                ScanResultRow(
                    productName: "有机苹果",
                    carbonImpact: "低碳",
                    impactColor: .success,
                    reward: "+5"
                )

                ScanResultRow(
                    productName: "塑料瓶装水",
                    carbonImpact: "高碳",
                    impactColor: .error,
                    reward: "+3"
                )
            }
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }
}

// MARK: - Scan Result Row
struct ScanResultRow: View {
    let productName: String
    let carbonImpact: String
    let impactColor: Color
    let reward: String

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(productName)
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)

                Text(carbonImpact)
                    .font(.captionBrand)
                    .foregroundColor(impactColor)
            }

            Spacer()

            HStack {
                Image(systemName: "leaf.fill")
                    .foregroundColor(.auxiliaryYellow)
                    .font(.caption)

                Text(reward)
                    .font(.captionBrand)
                    .foregroundColor(.auxiliaryYellow)
            }
        }
        .padding(.vertical, Theme.Spacing.sm)
    }
}

#Preview {
    ScanView()
        .stableBackground()
}
